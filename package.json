{"name": "abans-internship-form", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@supabase/supabase-js": "^2.39.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.0.8", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lucide-react": "^0.454.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.50.1", "react-router-dom": "^6.25.1", "react-signature-canvas": "^1.0.6", "tailwind-merge": "^2.5.5", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^22.0.0", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.17", "typescript": "^5.2.2", "vite": "^5.3.5"}}