import { useFieldArray, type UseFormReturn } from "react-hook-form"
import { FloatingInput } from "../ui/floating-input"
import { FloatingTextarea } from "../ui/floating-textarea"

interface ReferenceSectionProps {
  form: UseFormReturn<any>
}

export function ReferenceSection({ form }: ReferenceSectionProps) {
  const { fields } = useFieldArray({
    control: form.control,
    name: "references",
  });

  return (
    <div className="space-y-8">
      <div className="text-center">
        <p className="text-gray-600 dark:text-gray-400">
          Please provide details for at least 2 professional references
        </p>
      </div>

      {fields.map((field, index) => (
        <div key={field.id} className="bg-gray-50 dark:bg-gray-700 p-6 rounded-xl">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
            Reference {index + 1} {index < 2 && <span className="text-red-500">*</span>}
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FloatingInput
              label={`Name ${index < 2 ? "*" : ""}`}
              placeholder="Enter reference name"
              {...form.register(`references.${index}.name`)}
              error={(form.formState.errors.references as any)?.[index]?.name?.message}
            />
            <FloatingInput
              label={`Designation ${index < 2 ? "*" : ""}`}
              placeholder="Enter designation"
              {...form.register(`references.${index}.designation`)}
              error={(form.formState.errors.references as any)?.[index]?.designation?.message}
            />
            <FloatingInput
              label={`Company ${index < 2 ? "*" : ""}`}
              placeholder="Enter company name"
              {...form.register(`references.${index}.company`)}
              error={(form.formState.errors.references as any)?.[index]?.company?.message}
            />
            <FloatingInput
              label={`Contact Number ${index < 2 ? "*" : ""}`}
              placeholder="Enter contact number"
              {...form.register(`references.${index}.contact_no`)}
              error={(form.formState.errors.references as any)?.[index]?.contact_no?.message}
            />
            <FloatingInput
              label={`Email ${index < 2 ? "*" : ""}`}
              type="email"
              placeholder="Enter email address"
              {...form.register(`references.${index}.email`)}
              error={(form.formState.errors.references as any)?.[index]?.email?.message}
            />
            <FloatingTextarea
              label={`Address ${index < 2 ? "*" : ""}`}
              placeholder="Enter company address"
              {...form.register(`references.${index}.address`)}
              error={(form.formState.errors.references as any)?.[index]?.address?.message}
              rows={3}
            />
          </div>
        </div>
      ))}

      {form.formState.errors.references && (
        <p className="text-red-600 dark:text-red-400 text-sm text-center">{String(form.formState.errors.references.message)}</p>
      )}
    </div>
  )
}
