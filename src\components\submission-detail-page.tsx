import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { ArrowLeft } from 'lucide-react';
import { API_BASE_URL } from '../config/api';

// Helper component to display a single field
const DetailField = ({ label, value }: { label: string; value: any }) => {
  if (value === null || value === undefined || value === '') {
    value = 'N/A';
  }
  return (
    <div className="bg-slate-800/30 rounded-lg p-4 border border-slate-700/50">
      <h3 className="text-xs font-semibold text-cyan-300 uppercase tracking-wider mb-2">{label.replace(/_/g, ' ')}</h3>
      <p className="text-white text-sm leading-relaxed">{value.toString()}</p>
    </div>
  );
};

// Helper component for file downloads
const DownloadField = ({ label, s3Key, handleDownload }: { label: string; s3Key: string | null; handleDownload: (key: string) => void }) => {
  const hasFile = s3Key && s3Key.trim() !== '' && s3Key !== 'N/A' && s3Key !== 'null' && s3Key !== 'undefined';
  
  // Debug logging
  console.log(`${label} - s3Key:`, s3Key, 'hasFile:', hasFile);
  
  return (
    <div className="bg-slate-800/30 rounded-lg p-4 border border-slate-700/50">
      <h3 className="text-xs font-semibold text-cyan-300 uppercase tracking-wider mb-3">{label.replace(/_/g, ' ')}</h3>
      {hasFile ? (
        <div className="space-y-2">
          <button 
            onClick={() => handleDownload(s3Key)} 
            className="inline-flex items-center px-4 py-2 bg-cyan-600/20 hover:bg-cyan-600/30 border border-cyan-500/30 hover:border-cyan-400/50 rounded-md text-cyan-300 hover:text-cyan-200 text-sm font-medium transition-all duration-200 ease-in-out"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Download File
          </button>
          <p className="text-xs text-slate-400">Click to download the uploaded document</p>
        </div>
      ) : (
        <div className="text-center py-3">
          <svg className="w-8 h-8 text-slate-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p className="text-slate-500 text-sm">No file uploaded</p>
        </div>
      )}
    </div>
  );
};

export default function SubmissionDetailPage() {
  const { id } = useParams<{ id: string }>();
  const [submission, setSubmission] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSubmission = async () => {
      try {
        console.log('Fetching submission from:', `${API_BASE_URL}/api/forms/${id}`);
        const response = await fetch(`${API_BASE_URL}/api/forms/${id}`);
        if (!response.ok) {
          throw new Error(`Failed to fetch submission: ${response.status} ${response.statusText}`);
        }
        const result = await response.json();
        console.log('Raw submission data:', result.data);
        console.log('Document URLs in submission:', {
          photo_url: result.data?.photo_url,
          aadhar_url: result.data?.aadhar_url,
          pan_url: result.data?.pan_url,
          pdf_url: result.data?.pdf_url
        });
        setSubmission(result.data);
      } catch (err: any) {
        console.error('Error fetching submission:', err);
        setError(`Connection failed: ${err.message}. Make sure the backend server is running on port 3001.`);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchSubmission();
    }
  }, [id]);

  const handleDownload = async (s3Key: string) => {
    try {
      // Extract the actual S3 key from the stored URL format (s3://bucket-name/key)
      let key = s3Key;
      if (s3Key.startsWith('s3://')) {
        // Remove s3://bucket-name/ to get just the key
        const parts = s3Key.split('/');
        key = parts.slice(3).join('/'); // Skip s3:, empty string, and bucket name
      }
      
      console.log('Downloading file with key:', key);
      const response = await fetch(`${API_BASE_URL}/api/files/download-url?key=${encodeURIComponent(key)}`);
      if (!response.ok) {
        throw new Error('Failed to get download URL');
      }
      const result = await response.json();
      if (result.success && result.url) {
        window.open(result.url, '_blank');
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err: any) {
      console.error('Download error:', err);
      setError(`Failed to download file: ${err.message}`);
    }
  };

  if (loading) return (
    <div className="admin-panel min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-400 mx-auto mb-4"></div>
        <p className="text-slate-300">Loading submission details...</p>
      </div>
    </div>
  );
  if (error) return (
    <div className="admin-panel min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-8">
      <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
        <p className="text-red-400 font-medium">Error:</p>
        <p className="text-red-300 text-sm mt-1">{error}</p>
      </div>
    </div>
  );
  if (!submission) return (
    <div className="admin-panel min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-8">
      <p className="text-slate-300">Submission not found.</p>
    </div>
  );

  const renderComplexField = (label: string, data: string) => {
    try {
      const items = JSON.parse(data);
      if (!Array.isArray(items) || items.length === 0) {
        return (
          <section>
            <div className="flex items-center mb-6">
              <div className="bg-cyan-600/20 rounded-lg p-2 mr-3">
                <svg className="w-5 h-5 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-white">{label.replace(/_/g, ' ')}</h2>
            </div>
            <div className="bg-slate-800/30 rounded-lg p-6 border border-slate-700/50 text-center">
              <p className="text-slate-400">No information provided</p>
            </div>
          </section>
        );
      }
      return (
        <section>
          <div className="flex items-center mb-6">
            <div className="bg-cyan-600/20 rounded-lg p-2 mr-3">
              <svg className="w-5 h-5 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-white">{label.replace(/_/g, ' ')}</h2>
          </div>
          <div className="space-y-4">
            {items.map((item, index) => (
              <div key={index} className="bg-slate-800/30 rounded-lg p-4 border border-slate-700/50">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(item).map(([key, value]) => (
                    <div key={key} className="bg-slate-700/30 rounded p-3">
                      <h4 className="text-xs font-semibold text-cyan-300 uppercase tracking-wider mb-1">
                        {key.replace(/_/g, ' ')}
                      </h4>
                      <p className="text-white text-sm">
                        {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : String(value || 'N/A')}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </section>
      );
    } catch (e) {
      return <DetailField label={label} value={data} />;
    }
  };

  return (
    <div className="admin-panel min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header with ABANS branding */}
      <div className="bg-slate-900/90 backdrop-blur-sm border-b border-slate-700/50">
        <div className="w-full max-w-7xl mx-auto p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link 
                to="/admin" 
                className="inline-flex items-center px-3 py-2 bg-slate-800/50 hover:bg-slate-700/50 border border-slate-600/50 hover:border-slate-500/50 rounded-md text-slate-300 hover:text-white transition-all duration-200"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-cyan-400">
                  Submission Details
                </h1>
                <p className="text-slate-300 text-sm">
                  {submission.first_name} {submission.last_name} - Submitted on {new Date(submission.created_at).toLocaleDateString('en-GB')}
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-slate-400">Form ID</p>
              <p className="text-xs text-slate-500 font-mono">
                {submission.id?.substring(0, 8)}...
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Error display */}
      {error && (
        <div className="w-full max-w-7xl mx-auto p-6">
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-400 font-medium">Error:</p>
                <p className="text-red-300 text-sm mt-1">{error}</p>
              </div>
              <button 
                onClick={() => setError(null)} 
                className="text-red-400 hover:text-red-300 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="w-full max-w-7xl mx-auto p-6">
        <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700/50 shadow-2xl">
          <div className="p-8 space-y-10">
          {/* Personal Info */}
          <section>
            <div className="flex items-center mb-6">
              <div className="bg-cyan-600/20 rounded-lg p-2 mr-3">
                <svg className="w-5 h-5 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-white">Personal Information</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <DetailField label="First Name" value={submission.first_name} />
              <DetailField label="Middle Name" value={submission.middle_name} />
              <DetailField label="Last Name" value={submission.last_name} />
              <DetailField label="Father/Husband's Name" value={submission.father_husband_name} />
              <DetailField label="Date of Birth" value={new Date(submission.date_of_birth).toLocaleDateString()} />
              <DetailField label="Marital Status" value={submission.marital_status} />
              <DetailField label="Nationality" value={submission.nationality} />
              <DetailField label="Blood Group" value={submission.blood_group} />
              <DetailField label="Personal Email" value={submission.personal_email} />
              <DetailField label="Phone (Residence)" value={submission.phone_residence} />
              <DetailField label="Phone (Mobile)" value={submission.phone_mobile} />
              <DetailField label="Present Address" value={submission.present_address} />
              <DetailField label="Permanent Address" value={submission.permanent_address} />
            </div>
          </section>

          {/* Work & Company Info */}
          <section>
            <div className="flex items-center mb-6">
              <div className="bg-cyan-600/20 rounded-lg p-2 mr-3">
                <svg className="w-5 h-5 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-white">Company & Work Details</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <DetailField label="Company Name" value={submission.company_name} />
              <DetailField label="Department" value={submission.department} />
              <DetailField label="Date of Joining" value={submission.date_of_joining ? new Date(submission.date_of_joining).toLocaleDateString('en-GB') : 'N/A'} />
              <DetailField label="Place/Location" value={submission.place_location} />
              <DetailField label="UAN" value={submission.uan} />
              <DetailField label="Last PF No." value={submission.last_pf_no} />
            </div>
          </section>

          {/* Complex Fields */}
          {renderComplexField("Languages Known", submission.languages_known)}
          {renderComplexField("Family Dependants", submission.family_dependants)}
          {renderComplexField("Academic Qualifications", submission.academic_qualifications)}
          {renderComplexField("Professional Qualifications", submission.professional_qualifications)}
          {renderComplexField("Work Experience", submission.work_experience)}
          {renderComplexField("References", submission.references)}

          {/* Documents */}
          <section>
            <div className="flex items-center mb-6">
              <div className="bg-cyan-600/20 rounded-lg p-2 mr-3">
                <svg className="w-5 h-5 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-white">Uploaded Documents</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <DownloadField label="Photo" s3Key={submission.photo_url} handleDownload={handleDownload} />
              <DownloadField label="Aadhar Card" s3Key={submission.aadhar_url} handleDownload={handleDownload} />
              <DownloadField label="PAN Card" s3Key={submission.pan_url} handleDownload={handleDownload} />
              <DownloadField label="SSC Marksheet" s3Key={submission.ssc_marksheet_url} handleDownload={handleDownload} />
              <DownloadField label="SSC Passing Certificate" s3Key={submission.ssc_passing_url} handleDownload={handleDownload} />
              <DownloadField label="HSC Marksheet" s3Key={submission.hsc_marksheet_url} handleDownload={handleDownload} />
              <DownloadField label="HSC Passing Certificate" s3Key={submission.hsc_passing_url} handleDownload={handleDownload} />
              <DownloadField label="Graduation Marksheet" s3Key={submission.graduation_marksheet_url} handleDownload={handleDownload} />
              <DownloadField label="Graduation Passing Certificate" s3Key={submission.graduation_passing_url} handleDownload={handleDownload} />
              <DownloadField label="Post-Graduation Marksheet" s3Key={submission.postgrad_marksheet_url} handleDownload={handleDownload} />
              <DownloadField label="Post-Graduation Passing Certificate" s3Key={submission.postgrad_passing_url} handleDownload={handleDownload} />
              <DownloadField label="Salary Slips" s3Key={submission.salary_slips_urls} handleDownload={handleDownload} />
              <DownloadField label="Increment Letter" s3Key={submission.increment_letter_url} handleDownload={handleDownload} />
              <DownloadField label="Offer Letter" s3Key={submission.offer_letter_url} handleDownload={handleDownload} />
              <DownloadField label="Relieving Letter" s3Key={submission.relieving_letter_url} handleDownload={handleDownload} />
              <DownloadField label="Generated PDF" s3Key={submission.pdf_url} handleDownload={handleDownload} />
            </div>
          </section>
          </div>
        </Card>
      </div>
    </div>
  );
}
